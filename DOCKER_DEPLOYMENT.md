# HDSC Query App Docker 部署指南

## 镜像信息

- **镜像名称**: `hdsc-query-app:latest`
- **镜像大小**: 2.06GB
- **导出文件**: `hdsc-query-app-latest.tar` (约 464MB)
- **构建时间**: 2025年9月1日

## 快速部署

### 方式一：使用 Docker Compose（推荐）

```bash
# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方式二：直接使用 Docker 命令

```bash
# 运行容器
docker run -d \
  --name hdsc-query-app \
  -p 5000:5000 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/cache:/app/cache \
  -v $(pwd)/AHT:/app/AHT:ro \
  -v $(pwd)/BHT:/app/BHT:ro \
  -v $(pwd)/HZ:/app/HZ:ro \
  --restart unless-stopped \
  hdsc-query-app:latest
```

## 镜像传输和导入

### 导出镜像（已完成）
```bash
docker save -o hdsc-query-app-latest.tar hdsc-query-app:latest
```

### 在目标服务器导入镜像
```bash
# 导入镜像
docker load -i hdsc-query-app-latest.tar

# 验证导入
docker images hdsc-query-app
```

## 服务访问

- **应用地址**: http://localhost:5000
- **健康检查**: 容器会自动进行健康检查
- **日志位置**: `./logs` 目录
- **缓存位置**: `./cache` 目录

## 环境变量配置

主要环境变量：
- `FLASK_ENV=production`
- `FLASK_APP=run.py`
- `PYTHONPATH=/app`
- `TZ=Asia/Shanghai`

## 数据卷挂载

- `./logs:/app/logs` - 日志文件
- `./cache:/app/cache` - 缓存文件
- `./config.py:/app/config.py:ro` - 配置文件（只读）
- `./AHT:/app/AHT:ro` - AHT文档目录（只读）
- `./BHT:/app/BHT:ro` - BHT文档目录（只读）
- `./HZ:/app/HZ:ro` - HZ文档目录（只读）

## 故障排查

### 查看容器状态
```bash
docker ps -a
```

### 查看容器日志
```bash
docker logs hdsc-query-app
```

### 进入容器调试
```bash
docker exec -it hdsc-query-app /bin/bash
```

### 重启容器
```bash
docker restart hdsc-query-app
```

## 镜像特性

- **多阶段构建**: 使用优化的多阶段构建，减少最终镜像大小
- **安全性**: 使用非root用户运行应用
- **健康检查**: 内置健康检查机制
- **国内镜像源**: 配置了国内pip和npm镜像源，加速构建
- **生产就绪**: 使用Gunicorn作为WSGI服务器

## 注意事项

1. 确保目标服务器有足够的磁盘空间（至少3GB）
2. 确保端口5000未被占用
3. 首次启动可能需要较长时间进行初始化
4. 建议定期备份日志和缓存目录
5. 生产环境建议配置反向代理（如Nginx）

## 更新部署

```bash
# 停止当前服务
docker-compose down

# 导入新镜像
docker load -i hdsc-query-app-latest.tar

# 重新启动服务
docker-compose up -d
```