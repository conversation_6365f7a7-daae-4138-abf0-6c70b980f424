"""
串码查重服务模块
提供串码的存储、查重和管理功能
"""
import sqlite3
import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class SerialNumberService:
    """串码查重服务类"""
    
    def __init__(self, db_path: str = None):
        """
        初始化串码服务
        
        Args:
            db_path: 数据库文件路径，默认为项目根目录下的serial_numbers.db
        """
        if db_path is None:
            # 使用项目根目录下的数据库文件
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            db_path = os.path.join(project_root, 'serial_numbers.db')
        
        self.db_path = db_path
        self._init_database()
        logger.info(f"串码查重服务已初始化，数据库路径: {self.db_path}")
    
    def _init_database(self):
        """初始化数据库，创建表结构"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 创建串码表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS serial_numbers (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        serial_number TEXT UNIQUE NOT NULL,
                        created_at DATETIME NOT NULL,
                        created_by TEXT,
                        notes TEXT
                    )
                ''')
                
                # 创建索引以提高查询性能
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_serial_number 
                    ON serial_numbers(serial_number)
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_created_at 
                    ON serial_numbers(created_at)
                ''')
                
                conn.commit()
                logger.info("数据库表结构初始化完成")
                
        except Exception as e:
            logger.error(f"初始化数据库时发生错误: {str(e)}")
            raise
    
    @contextmanager
    def _get_db_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库连接错误: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def check_duplicate(self, serial_number: str) -> bool:
        """
        检查串码是否已存在

        Args:
            serial_number: 15位串码

        Returns:
            bool: True表示已存在（重复），False表示不存在
        """
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT COUNT(*) FROM serial_numbers WHERE serial_number = ?',
                    (serial_number,)
                )
                count = cursor.fetchone()[0]

                logger.info(f"串码查重结果: {serial_number} - {'已存在' if count > 0 else '不存在'}")
                return count > 0

        except Exception as e:
            logger.error(f"检查串码重复时发生错误: {str(e)}")
            raise

    def check_serial_number(self, serial_number: str) -> Dict[str, Any]:
        """
        检查串码是否存在，返回详细信息

        Args:
            serial_number: 15位串码

        Returns:
            Dict: 检查结果，包含是否存在和记录信息
        """
        try:
            # 先验证格式
            validation = validate_serial_number(serial_number)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': validation['error']
                }

            cleaned_serial = validation['cleaned_serial']

            # 检查是否存在
            record = self.get_serial_number_info(cleaned_serial)

            return {
                'success': True,
                'exists': record is not None,
                'record': record,
                'serial_number': cleaned_serial
            }

        except Exception as e:
            logger.error(f"检查串码时发生错误: {str(e)}")
            return {
                'success': False,
                'error': f'检查串码时发生错误: {str(e)}'
            }
    
    def add_serial_number(self, serial_number: str, created_by: str = None, notes: str = None) -> Dict[str, Any]:
        """
        添加新的串码记录
        
        Args:
            serial_number: 15位串码
            created_by: 创建者
            notes: 备注信息
            
        Returns:
            Dict: 包含操作结果的字典
        """
        try:
            # 先检查是否已存在
            if self.check_duplicate(serial_number):
                return {
                    'success': False,
                    'error': '已重复',
                    'message': f'串码 {serial_number} 已存在'
                }
            
            # 添加新记录
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO serial_numbers (serial_number, created_at, created_by, notes)
                    VALUES (?, ?, ?, ?)
                ''', (
                    serial_number,
                    datetime.now().isoformat(),
                    created_by,
                    notes
                ))
                
                conn.commit()
                record_id = cursor.lastrowid
                
                logger.info(f"成功添加串码: {serial_number}, ID: {record_id}")
                
                return {
                    'success': True,
                    'message': '已录入',
                    'record_id': record_id,
                    'serial_number': serial_number,
                    'created_at': datetime.now().isoformat()
                }
                
        except sqlite3.IntegrityError as e:
            # 处理唯一约束违反（并发情况下可能发生）
            logger.warning(f"串码 {serial_number} 违反唯一约束: {str(e)}")
            return {
                'success': False,
                'error': '已重复',
                'message': f'串码 {serial_number} 已存在'
            }
        except Exception as e:
            logger.error(f"添加串码时发生错误: {str(e)}")
            return {
                'success': False,
                'error': '系统错误',
                'message': f'添加串码时发生错误: {str(e)}'
            }
    
    def get_serial_number_info(self, serial_number: str) -> Optional[Dict[str, Any]]:
        """
        获取串码的详细信息
        
        Args:
            serial_number: 15位串码
            
        Returns:
            Dict: 串码信息，如果不存在则返回None
        """
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, serial_number, created_at, created_by, notes
                    FROM serial_numbers 
                    WHERE serial_number = ?
                ''', (serial_number,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'id': row['id'],
                        'serial_number': row['serial_number'],
                        'created_at': row['created_at'],
                        'created_by': row['created_by'],
                        'notes': row['notes']
                    }
                return None
                
        except Exception as e:
            logger.error(f"获取串码信息时发生错误: {str(e)}")
            raise
    
    def get_recent_records(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的串码记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List: 最近的串码记录列表
        """
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, serial_number, created_at, created_by, notes
                    FROM serial_numbers 
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))
                
                rows = cursor.fetchall()
                return [
                    {
                        'id': row['id'],
                        'serial_number': row['serial_number'],
                        'created_at': row['created_at'],
                        'created_by': row['created_by'],
                        'notes': row['notes']
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"获取最近记录时发生错误: {str(e)}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取串码统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 总记录数
                cursor.execute('SELECT COUNT(*) FROM serial_numbers')
                total_count = cursor.fetchone()[0]
                
                # 今日新增
                today = datetime.now().date().isoformat()
                cursor.execute('''
                    SELECT COUNT(*) FROM serial_numbers 
                    WHERE DATE(created_at) = ?
                ''', (today,))
                today_count = cursor.fetchone()[0]
                
                # 本周新增
                cursor.execute('''
                    SELECT COUNT(*) FROM serial_numbers 
                    WHERE DATE(created_at) >= DATE('now', '-7 days')
                ''')
                week_count = cursor.fetchone()[0]
                
                return {
                    'total_count': total_count,
                    'today_count': today_count,
                    'week_count': week_count,
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取统计信息时发生错误: {str(e)}")
            return {
                'total_count': 0,
                'today_count': 0,
                'week_count': 0,
                'last_updated': datetime.now().isoformat(),
                'error': str(e)
            }


def validate_serial_number(serial_number: str) -> Dict[str, Any]:
    """
    验证串码格式
    
    Args:
        serial_number: 待验证的串码
        
    Returns:
        Dict: 验证结果
    """
    if not serial_number:
        return {
            'valid': False,
            'error': '串码不能为空'
        }
    
    # 去除空格和特殊字符
    cleaned = ''.join(c for c in serial_number if c.isdigit())
    
    if len(cleaned) != 15:
        return {
            'valid': False,
            'error': f'串码必须为15位数字，当前为{len(cleaned)}位'
        }
    
    if not cleaned.isdigit():
        return {
            'valid': False,
            'error': '串码只能包含数字'
        }
    
    return {
        'valid': True,
        'cleaned_serial': cleaned
    }


# 创建全局服务实例
serial_service = SerialNumberService()
