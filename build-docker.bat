@echo off
echo ====================================
echo   太享查询系统 Docker 镜像封装工具
echo   HDSC Query App Docker Builder
echo ====================================
echo.

REM 检查Docker是否安装
where docker >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] Docker未安装或未添加到PATH环境变量中
    echo [ERROR] Docker is not installed or not in PATH
    echo 请先安装Docker Desktop并确保Docker服务正在运行
    pause
    exit /b 1
)

REM 检查Docker服务是否运行
docker info >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] Docker服务未运行
    echo [ERROR] Docker service is not running
    echo 请启动Docker Desktop
    pause
    exit /b 1
)

REM 设置镜像名称和标签
set IMAGE_NAME=hdsc-query-app
set IMAGE_TAG=latest
set EXPORT_FILE=%IMAGE_NAME%-%IMAGE_TAG%.tar

echo Docker服务运行正常，开始构建镜像...
echo Docker service is running, starting build...
echo.

REM 构建镜像
echo [步骤 1/3] 构建Docker镜像...
echo [Step 1/3] Building Docker image...
docker build -t %IMAGE_NAME%:%IMAGE_TAG% .

if %errorlevel% neq 0 (
    echo [错误] 镜像构建失败
    echo [ERROR] Docker image build failed
    pause
    exit /b 1
)

echo [成功] 镜像构建完成
echo [SUCCESS] Docker image built successfully
echo.

REM 显示镜像信息
echo [步骤 2/3] 显示镜像信息...
echo [Step 2/3] Showing image info...
docker images %IMAGE_NAME%:%IMAGE_TAG%
echo.

REM 导出镜像
echo [步骤 3/3] 导出镜像到文件...
echo [Step 3/3] Exporting image to file...
docker save -o %EXPORT_FILE% %IMAGE_NAME%:%IMAGE_TAG%

if %errorlevel% neq 0 (
    echo [错误] 镜像导出失败
    echo [ERROR] Docker image export failed
    pause
    exit /b 1
)

echo [成功] 镜像已导出到: %EXPORT_FILE%
echo [SUCCESS] Image exported to: %EXPORT_FILE%

REM 显示文件大小
for %%A in ("%EXPORT_FILE%") do (
    set size=%%~zA
)
set /a sizeMB=%size%/1024/1024
echo 文件大小: %sizeMB% MB
echo File size: %sizeMB% MB
echo.

echo ====================================
echo           封装完成！
echo        Packaging Complete!
echo ====================================
echo.
echo 生成的文件:
echo Generated files:
echo - Docker镜像: %IMAGE_NAME%:%IMAGE_TAG%
echo - 镜像文件: %EXPORT_FILE%
echo.
echo 使用方法:
echo Usage:
echo 1. 将 %EXPORT_FILE% 复制到目标机器
echo 2. 运行: docker load -i %EXPORT_FILE%
echo 3. 运行: docker run -d -p 5000:5000 %IMAGE_NAME%:%IMAGE_TAG%
echo.

pause
